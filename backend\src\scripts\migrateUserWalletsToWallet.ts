import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import UserWallet from '../models/userWalletModel';
import { logger } from '../utils/logger';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface MigrationStats {
  totalUsers: number;
  totalUserWallets: number;
  migratedUsers: number;
  migratedWallets: number;
  errors: string[];
  skippedWallets: number;
}

class UserWalletMigration {
  private stats: MigrationStats = {
    totalUsers: 0,
    totalUserWallets: 0,
    migratedUsers: 0,
    migratedWallets: 0,
    errors: [],
    skippedWallets: 0
  };

  async connectDatabase(): Promise<void> {
    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
      await mongoose.connect(mongoUri);
      logger.info('Connected to MongoDB for migration');
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnectDatabase(): Promise<void> {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }

  async runMigration(): Promise<MigrationStats> {
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Get all UserWallets
        const userWallets = await UserWallet.find({}).session(session);
        this.stats.totalUserWallets = userWallets.length;
        
        logger.info(`Found ${userWallets.length} UserWallets to migrate`);

        // Group UserWallets by userId
        const walletsByUser = this.groupWalletsByUser(userWallets);
        this.stats.totalUsers = Object.keys(walletsByUser).length;
        
        logger.info(`Found ${this.stats.totalUsers} unique users`);

        // Migrate each user's wallets
        for (const [userId, wallets] of Object.entries(walletsByUser)) {
          try {
            await this.migrateUserWallets(userId, wallets, session);
            this.stats.migratedUsers++;
            this.stats.migratedWallets += wallets.length;
          } catch (error) {
            const errorMsg = `Failed to migrate user ${userId}: ${error.message}`;
            logger.error(errorMsg);
            this.stats.errors.push(errorMsg);
          }
        }
      });

      logger.info('Migration completed successfully');
    } catch (error) {
      logger.error('Migration failed:', error);
      this.stats.errors.push(`Migration transaction failed: ${error.message}`);
    } finally {
      await session.endSession();
    }

    return this.stats;
  }

  private groupWalletsByUser(userWallets: any[]): { [userId: string]: any[] } {
    return userWallets.reduce((acc, wallet) => {
      const userId = wallet.userId.toString();
      if (!acc[userId]) {
        acc[userId] = [];
      }
      acc[userId].push(wallet);
      return acc;
    }, {} as { [userId: string]: any[] });
  }

  private async migrateUserWallets(userId: string, userWallets: any[], session: mongoose.ClientSession): Promise<void> {
    // Find existing Wallet document or create new one
    let wallet = await Wallet.findOne({ userId }).session(session);
    
    if (!wallet) {
      wallet = new Wallet({
        userId,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      logger.info(`Creating new Wallet document for user ${userId}`);
    } else {
      logger.info(`Found existing Wallet document for user ${userId}`);
    }

    // Process each UserWallet
    for (const userWallet of userWallets) {
      try {
        await this.migrateUserWallet(wallet, userWallet);
      } catch (error) {
        logger.error(`Error migrating UserWallet ${userWallet._id}:`, error);
        this.stats.skippedWallets++;
      }
    }

    // Save the updated wallet
    await wallet.save({ session });
    logger.info(`Successfully migrated ${userWallets.length} wallets for user ${userId}`);
  }

  private async migrateUserWallet(wallet: any, userWallet: any): Promise<void> {
    const currency = userWallet.currency;
    
    // Find existing asset or create new one
    let asset = wallet.assets.find((a: any) => a.symbol === currency);
    
    if (!asset) {
      // Create new asset
      asset = {
        symbol: currency,
        balance: userWallet.balance || 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'commission',
        network: userWallet.network || 'mainnet',
        address: userWallet.address,
        addresses: []
      };
      wallet.assets.push(asset);
      logger.info(`Created new asset ${currency} for wallet`);
    } else {
      // Update existing asset balance (accumulate if multiple UserWallets for same currency)
      asset.balance = (asset.balance || 0) + (userWallet.balance || 0);
      
      // Update primary address if not set
      if (!asset.address && userWallet.address) {
        asset.address = userWallet.address;
      }
      
      logger.info(`Updated existing asset ${currency} balance`);
    }

    // Add address to addresses array if it doesn't exist
    if (userWallet.address) {
      if (!asset.addresses) {
        asset.addresses = [];
      }

      // Check if address already exists
      const existingAddress = asset.addresses.find((addr: any) => addr.address === userWallet.address);
      
      if (!existingAddress) {
        const addressEntry = {
          address: userWallet.address,
          network: userWallet.network || 'mainnet',
          isDefault: userWallet.isDefault || asset.addresses.length === 0,
          isActive: userWallet.isActive !== false,
          qrCodeUrl: userWallet.qrCodeUrl || this.generateQRCodeUrl(userWallet.address),
          label: userWallet.label || `${currency} Wallet`,
          addressIndex: userWallet.addressIndex || asset.addresses.length,
          lastUpdated: userWallet.lastUpdated || new Date(),
          withdrawalEnabled: userWallet.withdrawalEnabled !== false
        };

        // If this is set as default, unset other defaults
        if (addressEntry.isDefault) {
          asset.addresses.forEach((addr: any) => {
            addr.isDefault = false;
          });
        }

        asset.addresses.push(addressEntry);
        logger.info(`Added address ${userWallet.address} to ${currency} asset`);
      } else {
        logger.info(`Address ${userWallet.address} already exists for ${currency}`);
      }
    }
  }

  private generateQRCodeUrl(address: string): string {
    return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(address)}`;
  }

  async validateMigration(): Promise<boolean> {
    try {
      logger.info('Validating migration...');
      
      // Count UserWallets
      const userWalletCount = await UserWallet.countDocuments();
      
      // Count Wallet assets
      const wallets = await Wallet.find({});
      let totalAssets = 0;
      let totalAddresses = 0;
      
      for (const wallet of wallets) {
        totalAssets += wallet.assets.length;
        for (const asset of wallet.assets) {
          if (asset.addresses) {
            totalAddresses += asset.addresses.length;
          }
        }
      }
      
      logger.info(`Validation results:`);
      logger.info(`- UserWallets: ${userWalletCount}`);
      logger.info(`- Wallet assets: ${totalAssets}`);
      logger.info(`- Total addresses: ${totalAddresses}`);
      logger.info(`- Migrated wallets: ${this.stats.migratedWallets}`);
      
      // Basic validation: migrated wallets should be <= UserWallets
      const isValid = this.stats.migratedWallets <= userWalletCount;
      
      if (isValid) {
        logger.info('✅ Migration validation passed');
      } else {
        logger.error('❌ Migration validation failed');
      }
      
      return isValid;
    } catch (error) {
      logger.error('Error during validation:', error);
      return false;
    }
  }

  printStats(): void {
    console.log('\n=== Migration Statistics ===');
    console.log(`Total Users: ${this.stats.totalUsers}`);
    console.log(`Total UserWallets: ${this.stats.totalUserWallets}`);
    console.log(`Migrated Users: ${this.stats.migratedUsers}`);
    console.log(`Migrated Wallets: ${this.stats.migratedWallets}`);
    console.log(`Skipped Wallets: ${this.stats.skippedWallets}`);
    console.log(`Errors: ${this.stats.errors.length}`);
    
    if (this.stats.errors.length > 0) {
      console.log('\n=== Errors ===');
      this.stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    const successRate = ((this.stats.migratedWallets / this.stats.totalUserWallets) * 100).toFixed(2);
    console.log(`\nSuccess Rate: ${successRate}%`);
  }
}

// Main execution function
async function main() {
  const migration = new UserWalletMigration();
  
  try {
    console.log('🚀 Starting UserWallet to Wallet migration...');
    
    await migration.connectDatabase();
    
    const stats = await migration.runMigration();
    
    migration.printStats();
    
    // Validate migration
    const isValid = await migration.validateMigration();
    
    if (isValid && stats.errors.length === 0) {
      console.log('\n✅ Migration completed successfully!');
      process.exit(0);
    } else {
      console.log('\n⚠️ Migration completed with issues. Please review the errors above.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await migration.disconnectDatabase();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  main();
}

export default UserWalletMigration;
