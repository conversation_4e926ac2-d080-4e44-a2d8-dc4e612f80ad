import mongoose, { Document, Schema } from 'mongoose';

export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  assets: {
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
    network?: string;
    address?: string;
    // Enhanced address management
    addresses?: {
      address: string;
      network: string;
      isDefault: boolean;
      isActive: boolean;
      qrCodeUrl?: string;
      label?: string;
      privateKey?: string; // Encrypted
      addressIndex?: number;
      lastUpdated?: Date;
      withdrawalEnabled?: boolean;
    }[];
  }[];
  totalCommissionEarned: number;
  totalInterestEarned: number;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getEarnedBalance(symbol: string): number;
  getAssetsWithEarnedBalance(): any[];
  // New methods for address management
  getAssetAddresses(symbol: string): any[];
  getDefaultAddress(symbol: string): any;
  addAddress(symbol: string, address: string, network?: string, isDefault?: boolean): Promise<IWallet>;
  updateAddress(symbol: string, addressId: string, updates: any): Promise<IWallet>;
}

const walletSchema = new Schema<IWallet>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    assets: [
      {
        symbol: {
          type: String,
          required: true,
          trim: true,
          uppercase: true,
        },
        balance: {
          type: Number,
          required: true,
          default: 0,
        },
        commissionBalance: {
          type: Number,
          default: 0,
        },
        interestBalance: {
          type: Number,
          default: 0,
        },
        mode: {
          type: String,
          enum: ['commission', 'interest'],
        },
        network: {
          type: String,
          trim: true,
        },
        address: {
          type: String,
          trim: true,
        },
        // Enhanced address management
        addresses: [{
          address: {
            type: String,
            trim: true,
            required: true
          },
          network: {
            type: String,
            trim: true,
            default: 'mainnet'
          },
          isDefault: {
            type: Boolean,
            default: false
          },
          isActive: {
            type: Boolean,
            default: true
          },
          qrCodeUrl: {
            type: String,
            trim: true
          },
          label: {
            type: String,
            trim: true,
            maxlength: 50
          },
          privateKey: {
            type: String,
            trim: true,
            select: false
          },
          addressIndex: {
            type: Number,
            default: 0,
            min: 0
          },
          lastUpdated: {
            type: Date,
            default: Date.now
          },
          withdrawalEnabled: {
            type: Boolean,
            default: true
          }
        }]
      },
    ],
    totalCommissionEarned: {
      type: Number,
      default: 0,
    },
    totalInterestEarned: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Set JSON and Object transforms
walletSchema.set('toJSON', {
  virtuals: true
});

walletSchema.set('toObject', {
  virtuals: true
});

// Instance method to get earned balance for a specific asset
walletSchema.methods.getEarnedBalance = function(symbol: string): number {
  const asset = this.assets.find((a: any) => a.symbol === symbol);
  if (!asset) return 0;
  return (asset.commissionBalance || 0) + (asset.interestBalance || 0);
};

// Instance method to get all assets with earned balance calculation
walletSchema.methods.getAssetsWithEarnedBalance = function() {
  return this.assets.map((asset: any) => ({
    ...asset.toObject(),
    totalEarnings: (asset.commissionBalance || 0) + (asset.interestBalance || 0)
  }));
};

// Instance method to get addresses for a specific asset
walletSchema.methods.getAssetAddresses = function(symbol: string) {
  const asset = this.assets.find((a: any) => a.symbol === symbol.toUpperCase());
  return asset?.addresses || [];
};

// Instance method to get default address for a specific asset
walletSchema.methods.getDefaultAddress = function(symbol: string) {
  const asset = this.assets.find((a: any) => a.symbol === symbol.toUpperCase());
  if (!asset || !asset.addresses) return null;
  return asset.addresses.find((addr: any) => addr.isDefault && addr.isActive) || null;
};

// Instance method to add address to an asset
walletSchema.methods.addAddress = async function(
  symbol: string,
  address: string,
  network: string = 'mainnet',
  isDefault: boolean = false
) {
  let asset = this.assets.find((a: any) => a.symbol === symbol.toUpperCase());

  if (!asset) {
    // Create new asset if it doesn't exist
    asset = {
      symbol: symbol.toUpperCase(),
      balance: 0,
      commissionBalance: 0,
      interestBalance: 0,
      mode: 'commission',
      network,
      address,
      addresses: []
    };
    this.assets.push(asset);
  }

  if (!asset.addresses) {
    asset.addresses = [];
  }

  // If setting as default, unset other defaults
  if (isDefault) {
    asset.addresses.forEach((addr: any) => {
      addr.isDefault = false;
    });
  }

  // Add new address
  asset.addresses.push({
    address,
    network,
    isDefault,
    isActive: true,
    lastUpdated: new Date(),
    withdrawalEnabled: true,
    addressIndex: asset.addresses.length
  });

  return await this.save();
};

// Instance method to update address
walletSchema.methods.updateAddress = async function(
  symbol: string,
  addressId: string,
  updates: any
) {
  const asset = this.assets.find((a: any) => a.symbol === symbol.toUpperCase());
  if (!asset || !asset.addresses) {
    throw new Error(`Asset ${symbol} not found`);
  }

  const addressIndex = asset.addresses.findIndex((addr: any) => addr._id.toString() === addressId);
  if (addressIndex === -1) {
    throw new Error(`Address not found`);
  }

  // If setting as default, unset other defaults
  if (updates.isDefault) {
    asset.addresses.forEach((addr: any, index: number) => {
      if (index !== addressIndex) {
        addr.isDefault = false;
      }
    });
  }

  // Update address
  Object.assign(asset.addresses[addressIndex], updates, { lastUpdated: new Date() });

  return await this.save();
};

const Wallet = mongoose.model<IWallet>('Wallet', walletSchema);

export default Wallet;
